import axios, { AxiosInstance } from 'axios';
import { getApiUrl, debugLog } from '../utils/env';
import { Alert } from 'react-native';
import store from '../redux/store';
import { logout } from '../redux/slices/authSlice';

interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
}

class TokenRefreshService {
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value: { access_token: string; refresh_token: string } | null) => void;
    reject: (error: any) => void;
  }> = [];

  /**
   * Refresh the access token using the provided refresh token
   */
  async refreshToken(refreshToken: string): Promise<{ access_token: string; refresh_token: string } | null> {
    try {
      if (!refreshToken) {
        debugLog('No refresh token provided');
        return null;
      }

      const response = await axios.post<RefreshTokenResponse>(
        `${getApiUrl()}/auth/refresh`,
        { refresh_token: refreshToken },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      const { access_token, refresh_token: newRefreshToken } = response.data;

      debugLog('Token refreshed successfully');
      return { access_token, refresh_token: newRefreshToken };
    } catch (error: any) {
      debugLog('Token refresh failed:', error.response?.data || error.message);

      // Check if this is a forced logout scenario (invalid refresh token)
      if (error.response?.status === 401 || error.response?.status === 403) {
        // Show user-friendly message about being logged out from another device
        Alert.alert(
          'Session Expired',
          'You have been logged out because you signed in from another device. Please log in again.',
          [{ text: 'OK' }]
        );
        
        // Dispatch logout action to clear Redux state
        store.dispatch(logout());
      }

      return null;
    }
  }

  /**
   * Handle token refresh with queue to prevent multiple simultaneous refresh attempts
   */
  async handleTokenRefresh(refreshToken: string): Promise<{ access_token: string; refresh_token: string } | null> {
    if (this.isRefreshing) {
      // If already refreshing, wait for the current refresh to complete
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const result = await this.refreshToken(refreshToken);
      
      if (result) {
        // Process the failed queue with the new result
        this.processQueue(null, result);
        return result;
      } else {
        // Process the failed queue with error
        this.processQueue(new Error('Token refresh failed'), null);
        return null;
      }
    } catch (error) {
      this.processQueue(error, null);
      return null;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Process the queue of failed requests
   */
  private processQueue(error: any, result: { access_token: string; refresh_token: string } | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(result);
      }
    });
    
    this.failedQueue = [];
  }

  /**
   * Logout by revoking refresh token
   */
  async logout(refreshToken: string): Promise<void> {
    try {
      if (refreshToken) {
        // Call logout endpoint to revoke refresh token
        await axios.post(
          `${getApiUrl()}/auth/logout`,
          { refresh_token: refreshToken },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }
    } catch (error) {
      debugLog('Logout API call failed:', error);
      // Continue with cleanup even if API call fails
    }
  }
}

export const tokenRefreshService = new TokenRefreshService();
