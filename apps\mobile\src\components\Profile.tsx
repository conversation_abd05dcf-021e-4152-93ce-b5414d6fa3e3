import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser } from '../redux/slices/authSlice';
import {
  requestNotificationPermission,
  selectFCMPermission,
  selectFCMInitialized,
  selectFCMRegistered,
  selectFCMLoading
} from '../redux/slices/fcmSlice';
import { ProfileNavigationProp, ProfileRouteProp } from '../navigation/types';
import { shadows, radius, spacing, typography, useTheme } from '../theme';
import LogoutModal from './LogoutModal';
import ClearChatModal from './ClearChatModal';

interface ProfileProps {
  navigation: ProfileNavigationProp;
  route: ProfileRouteProp;
}

const Profile: React.FC<ProfileProps> = ({ navigation, route }) => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectAuthUser);
  const { username } = route.params;
  const { colors, isDark, toggleTheme } = useTheme();

  // FCM selectors
  const fcmPermission = useAppSelector(selectFCMPermission);
  const fcmInitialized = useAppSelector(selectFCMInitialized);
  const fcmRegistered = useAppSelector(selectFCMRegistered);
  const fcmLoading = useAppSelector(selectFCMLoading);

  const [showLogoutModal, setShowLogoutModal] = useState<boolean>(false);
  const [showClearDataModal, setShowClearDataModal] = useState<boolean>(false);
  const [clearingData, setClearingData] = useState<boolean>(false);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  const confirmLogout = () => {
    dispatch(logout());
    setShowLogoutModal(false);
  };

  const handleClearData = () => {
    setShowClearDataModal(true);
  };

  const confirmClearData = async () => {
    try {
      setClearingData(true);

      // Import the database service
      const { chatDBService } = await import('../database/service');

      // Clear all data from the database
      await chatDBService.clearAllData();

      Alert.alert('Success', 'All data has been cleared');
      setShowClearDataModal(false);
    } catch (error) {
      console.error('Error clearing data:', error);
      Alert.alert('Error', 'Failed to clear data');
    } finally {
      setClearingData(false);
    }
  };

  const handleNotificationPermission = async () => {
    if (fcmLoading) return;

    try {
      await (dispatch(requestNotificationPermission() as any) as any).unwrap();
      Alert.alert('Success', 'Notification permission updated');
    } catch (error: any) {
      Alert.alert('Error', 'Failed to update notification permission');
    }
  };

  const getAvatarText = () => {
    return username?.charAt(0).toUpperCase() || '?';
  };

  const isOwnProfile = currentUser === username;
  const styles = createStyles(colors);

  return (
    <SafeAreaView style={styles.container}>

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="chevron-left" size={28} color={colors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* User Info */}
        {isOwnProfile ? (
          <View style={styles.userSection}>
            <View style={styles.userAvatar}>
              <Text style={styles.avatarText}>{getAvatarText()}</Text>
            </View>
            <Text style={styles.username}>{username}</Text>
          </View>
        ) : (
          <View style={styles.userSection}>
            <View style={styles.userAvatarOther}>
              <Text style={styles.avatarTextOther}>{getAvatarText()}</Text>
            </View>
            <Text style={styles.username}>{username}</Text>
          </View>
        )}


        {/* Settings Sections - Only show for own profile */}
        {isOwnProfile && (
          <>
            {/* Privacy Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Privacy</Text>

              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>Recall Limit</Text>
                  <Text style={styles.settingValue}>Set Limit</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
                  <Text style={styles.settingLabel}>Clear Data</Text>
                  <Icon name="delete" size={20} color={colors.danger} />
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
                  <Text style={styles.settingLabel}>Disappearing Messages</Text>
                  <Text style={styles.settingValue}>24h</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Appearance Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Appearance</Text>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem} onPress={toggleTheme}>
                  <Text style={styles.settingLabel}>Dark Mode</Text>
                  <Text style={styles.settingValue}>{isDark ? 'On' : 'Off'}</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Notifications Section */}
            <View style={styles.settingGroup}>
              <Text style={styles.groupTitle}>Notifications</Text>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={handleNotificationPermission}
                  disabled={fcmLoading}
                >
                  <Text style={styles.settingLabel}>Push Notifications</Text>
                  <Text style={[
                    styles.settingValue,
                    !fcmPermission && styles.settingValueDisabled
                  ]}>
                    {fcmLoading ? 'Loading...' : fcmPermission ? 'Enabled' : 'Disabled'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>FCM Status</Text>
                  <Text style={[
                    styles.settingValue,
                    !fcmInitialized && styles.settingValueDisabled
                  ]}>
                    {fcmInitialized ? (fcmRegistered ? 'Connected' : 'Ready') : 'Not Ready'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>Emoji Reactions</Text>
                  <Text style={styles.settingValue}>On</Text>
                </TouchableOpacity>
              </View>



              {/* Safety & Support Section */}
              <View style={styles.settingGroup}>
                <Text style={styles.groupTitle}>Safety & Support</Text>
                <View style={styles.settingControlGroup}>
                  <TouchableOpacity style={styles.settingItem}>
                    <Text style={styles.settingLabel}>Block</Text>
                    <Text style={styles.settingValue}></Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.settingItem}>
                    <Text style={styles.settingLabel}>Report</Text>
                    <Text style={styles.settingValue}></Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={[styles.settingItem, styles.dangerItem]}>
                    <Text style={[styles.settingLabel, styles.dangerText]}>Delete User</Text>
                    <Icon name="delete-forever" size={20} color={colors.danger} />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Logout Section */}
              <View style={styles.logoutSection}>
                <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                  <Text style={styles.logoutButtonText}>Logout</Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}

        {/* For other users' profiles */}
        {!isOwnProfile && (
          <View style={styles.settingGroup}>
            <Text style={styles.groupTitle}>Actions</Text>
            <View style={styles.settingControlGroupOther}>
              <TouchableOpacity style={styles.settingItemOther}>
                <Text style={styles.settingLabel}>Block User</Text>
                <Text style={styles.settingValue}></Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingItemOther}>
                <Text style={styles.settingLabel}>Report User</Text>
                <Text style={styles.settingValue}></Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Modals */}
      <LogoutModal
        isVisible={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={confirmLogout}
      />

      <ClearChatModal
        isVisible={showClearDataModal}
        onClose={() => setShowClearDataModal(false)}
        onConfirm={confirmClearData}
        loading={clearingData}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingHorizontal: spacing.md,
    paddingTop: Platform.OS === 'ios' ? spacing.sm : spacing.lg, // Proper status bar spacing
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.sm,
  },
  backButton: {
    padding: spacing.xxs,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    ...typography.h2,
    fontSize: 18,
    fontFamily: 'Outfit-Medium',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button for centering
  },
  content: {
    flex: 1,
  },
  userSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    backgroundColor: colors.cardBackground,
    marginBottom: spacing.md,
  },
  userAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryLight3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarText: {
    color: colors.primaryDark1,
    fontSize: 32,
    fontFamily: 'Outfit-Bold',
  },
  userAvatarOther: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.toneLight3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarTextOther: {
    color: colors.toneDark1,
    fontSize: 32,
    fontFamily: 'Outfit-Bold',
  },
  username: {
    ...typography.h1,
    fontSize: 24,
    fontFamily: 'Outfit-Bold',
    color: colors.toneDark2,
  },
  settingGroup: {
    marginBottom: spacing.md,
  },
  settingControlGroup: {
    backgroundColor: colors.primaryLight3,
    marginHorizontal: spacing.lg,
    borderRadius: radius.md,
  },
  settingControlGroupOther: {
    backgroundColor: colors.toneLight3,
    marginHorizontal: spacing.lg,
    borderRadius: radius.md,
  },
  groupTitle: {
    fontSize: 12,
    fontFamily: 'Outfit-Medium',
    color: colors.toneDark1,
    letterSpacing: 0.5,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  settingItemOther: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  settingLabel: {
    ...typography.body,
    color: colors.primaryDark3,
    flex: 1,
  },
  settingValue: {
    ...typography.body,
    color: colors.primary,
    fontFamily: 'Outfit-Medium',
  },
  settingValueDisabled: {
    color: colors.textSecondary,
  },
  dangerItem: {
    borderBottomWidth: 0,
  },
  dangerText: {
    color: colors.danger,
  },
  logoutSection: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  logoutButton: {
    paddingVertical: spacing.md,
    borderRadius: radius.round,
    alignItems: 'center',
  },
  logoutButtonText: {
    color: colors.toneDark1,
    fontSize: 16,
    fontFamily: 'Outfit-Medium',
  },
});

export default Profile;
